import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from "../config/db";
import { UserWallet } from './UserWallet';
import { DeliveryLineCalculator, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';

interface DeliveryLineAttributes {
  id: number;
  walletId: number;
  level: number; // 出货线等级
  deliverySpeed: number; // 出货速度（秒/次）
  blockUnit: number; // 方块单位（牛奶/方块）
  blockPrice: number; // 方块价格（GEM/方块）
  upgradeCost: number; // 升级费用
  lastDeliveryTime: Date; // 上次出货时间
  pendingMilk: number; // 待处理的牛奶量
  pendingBlocks: number; // 待出售的牛奶方块数量
  createdAt?: Date;
  updatedAt?: Date;
}

interface DeliveryLineCreationAttributes extends Optional<DeliveryLineAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

class DeliveryLine extends Model<DeliveryLineAttributes, DeliveryLineCreationAttributes> implements DeliveryLineAttributes {
  public id!: number;
  public walletId!: number;
  public level!: number;
  public deliverySpeed!: number;
  public blockUnit!: number;
  public blockPrice!: number;
  public upgradeCost!: number;
  public lastDeliveryTime!: Date;
  public pendingMilk!: number;
  public pendingBlocks!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 计算速度增长百分比
  public calculateSpeedGrowthPercentage(): number {
    // 计算为(1.01)^(level-1) * 100%
    return Math.pow(1.01, this.level - 1) * 100;
  }

  // 升级出货线
  public upgrade(): void {
    this.level += 1;

    // 使用BigNumber进行高精度计算
    this.blockUnit = DeliveryLineCalculator.calculateUpgradedUnit(this.blockUnit);
    this.blockPrice = DeliveryLineCalculator.calculateUpgradedPrice(this.blockPrice);
    this.deliverySpeed = DeliveryLineCalculator.calculateUpgradedSpeed(this.deliverySpeed);
    this.upgradeCost = DeliveryLineCalculator.calculateUpgradeCost(this.upgradeCost);
  }

  // 添加牛奶到出货线
  public addMilk(milkAmount: number): number {
    this.pendingMilk += milkAmount;
    
    // 计算可以创建的牛奶方块数量
    const newBlocks = Math.floor(this.pendingMilk / this.blockUnit);
    this.pendingBlocks += newBlocks;
    this.pendingMilk -= newBlocks * this.blockUnit;
    
    return newBlocks;
  }

  // 计算当前可以出售的牛奶方块数量
  public calculateDeliveredBlocks(): number {
    const now = new Date();
    
    // 确保lastDeliveryTime是有效的Date对象
    if (!(this.lastDeliveryTime instanceof Date) || isNaN(this.lastDeliveryTime.getTime())) {
      this.lastDeliveryTime = new Date();
      return 0;
    }
    
    const timeDiff = (now.getTime() - this.lastDeliveryTime.getTime()) / 1000; // 转换为秒
    const cycles = Math.floor(timeDiff / this.deliverySpeed); // 完成的出货周期数
    
    if (cycles <= 0 || this.pendingBlocks <= 0) {
      return 0;
    }
    
    // 计算本次可以出售的方块数量
    const deliveredBlocks = Math.min(cycles, this.pendingBlocks);
    
    // 更新上次出货时间
    this.lastDeliveryTime = new Date(this.lastDeliveryTime.getTime() + deliveredBlocks * this.deliverySpeed * 1000);
    
    return deliveredBlocks;
  }

  // 出售牛奶方块获得GEM
  public deliverBlocks(): number {
    const deliveredBlocks = this.calculateDeliveredBlocks();
    if (deliveredBlocks <= 0) {
      return 0;
    }
    
    // 计算获得的GEM数量
    const earnedGem = deliveredBlocks * this.blockPrice;
    
    // 减少待出售的方块数量
    this.pendingBlocks -= deliveredBlocks;
    
    return earnedGem;
  }

  // 计算离线收益
  public calculateOfflineEarnings(offlineTime: number): number {
    // 确保lastDeliveryTime是有效的Date对象
    if (!(this.lastDeliveryTime instanceof Date) || isNaN(this.lastDeliveryTime.getTime())) {
      this.lastDeliveryTime = new Date();
      return 0;
    }
    
    // 计算离线期间可以完成的出货周期数
    const cycles = Math.floor(offlineTime / this.deliverySpeed);
    
    if (cycles <= 0 || this.pendingBlocks <= 0) {
      return 0;
    }
    
    // 计算离线期间可以出售的方块数量
    const deliveredBlocks = Math.min(cycles, this.pendingBlocks);
    
    // 计算获得的GEM数量
    const earnedGem = deliveredBlocks * this.blockPrice;
    
    // 减少待出售的方块数量
    this.pendingBlocks -= deliveredBlocks;
    
    // 更新上次出货时间
    const now = new Date();
    this.lastDeliveryTime = new Date(Math.min(
      now.getTime(),
      this.lastDeliveryTime.getTime() + deliveredBlocks * this.deliverySpeed * 1000
    ));
    
    return earnedGem;
  }
}

DeliveryLine.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'user_wallets',
        key: 'id',
      },
    },
    level: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 1,
    },
    deliverySpeed: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 5, // 5秒/次
    },
    blockUnit: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 5, // 5牛奶/方块
    },
    blockPrice: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 5, // 5 GEM/方块
    },
    upgradeCost: {
      type: DataTypes.DECIMAL(20, 3),
      allowNull: false,
      defaultValue: 500, // 初始升级费用
    },
    lastDeliveryTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    pendingMilk: {
      type: DataTypes.DECIMAL(20, 3),
      allowNull: false,
      defaultValue: 0,
    },
    pendingBlocks: {
      type: DataTypes.DECIMAL(20, 3),
      allowNull: false,
      defaultValue: 0,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'delivery_lines',
    timestamps: true,
  }
);



export { DeliveryLine };