// src/services/batchResourceUpdateService.ts
import { UserWallet } from '../models/UserWallet';
import { DeliveryLine } from '../models/DeliveryLine';
import { FarmPlot } from '../models/FarmPlot';
import { WalletHistory } from '../models/WalletHistory';
import { sequelize } from '../config/db';
import { Transaction, Op } from 'sequelize';
import BigNumber from 'bignumber.js';
import { createBigNumber, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';
import dayjs from 'dayjs';

// GEM限制常量
const GEM_LIMITS = {
  SINGLE_UPDATE_MAX: 10000,      // 单次更新上限
  DAILY_CUMULATIVE_MAX: 50000,   // 每日累计上限
  WALLET_TOTAL_MAX: 1000000      // 钱包总量上限
};

/**
 * 牛奶操作对象
 */
export interface MilkOperations {
  produce?: number;  // 农场生产量
  consume?: number;  // 出货线消耗量
}

/**
 * 批量资源更新请求参数
 */
export interface BatchResourceUpdateRequest {
  gemRequest?: number;                 // 前端请求的GEM增量（只能为非负数）
  milkOperations?: MilkOperations;     // 前端请求的牛奶操作（只支持对象形式）
}

/**
 * 批量资源更新响应数据
 */
export interface BatchResourceUpdateResponse {
  success: boolean;
  data: {
    beforeUpdate: {
      gem: number;
      pendingMilk: number;
      lastActiveTime: string | null;
    };
    afterUpdate: {
      gem: number;
      pendingMilk: number;
      lastActiveTime: string;
    };
    changes: {
      usedSystemCalculation?: boolean; // 是否使用了系统计算值（因请求不合理）
      systemCalculationReason?: string; // 使用系统计算的原因
      productionRates: {
        farmMilkPerSecond: number; // 农场每秒牛奶产量
        deliveryMilkPerSecond: number; // 出货线每秒牛奶消耗量
        gemPerSecond: number; // 出货线每秒GEM产量
        timeElapsedSeconds: number; // 实际经过的秒数
      };
      details: {
        gem: {
          increased: number; // GEM增加量
        };
        milk: {
          increased: number; // 牛奶增加量
          decreased: number; // 牛奶减少量
        };
      };
    };
    timestamp: string;
  };
  message: string;
}

/**
 * 批量资源更新服务类
 */
export class BatchResourceUpdateService {
  /**
   * 验证请求参数
   */
  private static validateRequest(request: BatchResourceUpdateRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 如果没有提供任何参数，允许无参数调用（系统自动计算）
    // 这种情况下会根据时间自动计算资源

    // 验证 gemRequest - 只能为非负数（GEM只会增加不会减少）
    if (request.gemRequest !== undefined) {
      if (typeof request.gemRequest !== 'number' || isNaN(request.gemRequest)) {
        errors.push('gemRequest 必须是有效的数值');
      } else if (request.gemRequest < 0) {
        errors.push('gemRequest 必须为非负数（GEM只会增加不会减少）');
      }
    }

    // 验证 milkOperations - 只支持对象形式
    if (request.milkOperations !== undefined) {
      if (typeof request.milkOperations === 'object' && request.milkOperations !== null) {
        // 对象形式：验证 produce 和 consume 字段
        const milkOps = request.milkOperations as MilkOperations;

        if (milkOps.produce !== undefined) {
          if (typeof milkOps.produce !== 'number' || isNaN(milkOps.produce) || milkOps.produce < 0) {
            errors.push('milkOperations.produce 必须是非负数');
          }
        }

        if (milkOps.consume !== undefined) {
          if (typeof milkOps.consume !== 'number' || isNaN(milkOps.consume) || milkOps.consume < 0) {
            errors.push('milkOperations.consume 必须是非负数');
          }
        }

        // 至少需要提供一个操作
        if (milkOps.produce === undefined && milkOps.consume === undefined) {
          errors.push('milkOperations 对象必须包含 produce 或 consume 字段');
        }
      } else {
        errors.push('milkOperations 必须是包含 produce/consume 字段的对象');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }



  /**
   * 计算用户今日已累计的GEM更新量
   */
  private static async calculateDailyGemUpdates(walletId: number, transaction: Transaction): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dailyGemHistory = await WalletHistory.findAll({
      where: {
        walletId,
        currency: 'GEM',
        action: 'IN',
        category: 'BATCH_UPDATE',
        createdAt: {
          [Op.gte]: today,
          [Op.lt]: tomorrow
        }
      },
      transaction
    });

    let totalDailyGem = 0;
    dailyGemHistory.forEach(record => {
      totalDailyGem += record.amount || 0;
    });

    return formatToThreeDecimalsNumber(totalDailyGem);
  }

  /**
   * 计算农场区块的总产能（每秒）- 保留用于兼容性
   */
  private static async calculateFarmProductionCapacity(walletId: number, transaction: Transaction): Promise<number> {
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    let totalProductionPerSecond = 0;
    for (const plot of farmPlots) {
      // 每秒产量 = 牛奶产量 ÷ 产出时间
      const plotProductionPerSecond = plot.milkProduction / plot.productionSpeed;
      totalProductionPerSecond += plotProductionPerSecond;
    }

    return formatToThreeDecimalsNumber(totalProductionPerSecond);
  }

  /**
   * 基于周期计算农场实际产量（正确的逻辑）
   */
  private static async calculateFarmProductionByCycles(walletId: number, timeElapsedSeconds: number, transaction: Transaction): Promise<number> {
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    let totalProduction = 0;
    for (const plot of farmPlots) {
      // 计算完整周期数：时间 ÷ 生产速度
      const completeCycles = Math.floor(timeElapsedSeconds / plot.productionSpeed);
      // 实际产量 = 完整周期数 × 每周期产量
      const plotProduction = completeCycles * plot.milkProduction;
      totalProduction += plotProduction;
    }

    return formatToThreeDecimalsNumber(totalProduction);
  }

  /**
   * 基于周期计算出货线实际处理量（正确的逻辑）
   */
  private static calculateDeliveryProcessingByCycles(deliveryLine: any, timeElapsedSeconds: number): { milkConsumed: number; gemProduced: number } {
    if (!deliveryLine) {
      return { milkConsumed: 0, gemProduced: 0 };
    }

    // 计算完整周期数：时间 ÷ 出货速度
    const completeCycles = Math.floor(timeElapsedSeconds / deliveryLine.deliverySpeed);

    // 实际消耗和产出 = 完整周期数 × 每周期量
    const milkConsumed = completeCycles * deliveryLine.blockUnit;
    const gemProduced = completeCycles * deliveryLine.blockPrice;

    return {
      milkConsumed: formatToThreeDecimalsNumber(milkConsumed),
      gemProduced: formatToThreeDecimalsNumber(gemProduced)
    };
  }

  /**
   * 协调计算农场和出货线的实际产出（考虑生产-消费平衡）
   */
  private static async calculateCoordinatedProduction(
    walletId: number,
    timeElapsedSeconds: number,
    currentPendingMilk: number,
    transaction: Transaction
  ): Promise<{
    farmProduced: number;
    deliveryConsumed: number;
    gemProduced: number;
    finalPendingMilk: number
  }> {
    // 获取农场和出货线数据
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

    // 每个用户都有出货线，如果查询失败则抛出错误
    if (!deliveryLine) {
      throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
    }

    // 简化的协调计算：先计算农场总产量，再计算出货线能处理多少
    let totalFarmProduced = 0;
    for (const plot of farmPlots) {
      const farmCycles = Math.floor(timeElapsedSeconds / plot.productionSpeed);
      totalFarmProduced += farmCycles * plot.milkProduction;
    }

    // 计算可用的牛奶总量（初始库存 + 农场产量）
    const totalAvailableMilk = currentPendingMilk + totalFarmProduced;

    // 计算出货线理论处理能力
    const deliveryCycles = Math.floor(timeElapsedSeconds / deliveryLine.deliverySpeed);
    const theoreticalDeliveryConsumption = deliveryCycles * deliveryLine.blockUnit;

    // 实际消耗不能超过可用牛奶
    const actualDeliveryConsumption = Math.min(theoreticalDeliveryConsumption, totalAvailableMilk);

    // 计算实际GEM产出（基于实际消耗的牛奶）
    const actualGemProduction = (actualDeliveryConsumption / deliveryLine.blockUnit) * deliveryLine.blockPrice;

    // 计算最终库存
    const finalPendingMilk = totalAvailableMilk - actualDeliveryConsumption;

    return {
      farmProduced: formatToThreeDecimalsNumber(totalFarmProduced),
      deliveryConsumed: formatToThreeDecimalsNumber(actualDeliveryConsumption),
      gemProduced: formatToThreeDecimalsNumber(actualGemProduction),
      finalPendingMilk: formatToThreeDecimalsNumber(finalPendingMilk)
    };
  }

  /**
   * 计算用户的在线状态和离线时间
   */
  private static calculateUserOnlineStatus(lastActiveTime: Date | null): {
    isOnline: boolean;
    offlineTimeInSeconds: number;
    offlineTimeInHours: number;
  } {
    if (!lastActiveTime) {
      return {
        isOnline: false,
        offlineTimeInSeconds: 0,
        offlineTimeInHours: 0
      };
    }

    const now = new Date();
    const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : new Date(lastActiveTime);
    const offlineTimeInSeconds = Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000);

    // 如果在1分钟内有活动，则视为在线
    const isOnline = offlineTimeInSeconds < 60;
    const offlineTimeInHours = offlineTimeInSeconds / 3600;

    return {
      isOnline,
      offlineTimeInSeconds,
      offlineTimeInHours
    };
  }

  /**
   * 解析milkOperations参数，转换为净增量
   */
  private static parseMilkOperations(milkOperations: MilkOperations | undefined): number {
    if (milkOperations === undefined) {
      return 0;
    }

    // 对象形式：计算净增量 = produce - consume
    const produce = milkOperations.produce || 0;
    const consume = milkOperations.consume || 0;
    return produce - consume;
  }

  /**
   * 验证GEM增量是否合理
   */
  private static async validateGemIncrement(
    tempGem: number,
    timeElapsedHours: number,
    walletId: number,
    transaction: Transaction,
    language: string = 'zh'
  ): Promise<{ isValid: boolean; reason?: string; adjustedTempGem: number }> {

    // 计算出货线处理能力
    const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

    // 每个用户都有出货线，如果查询失败则抛出错误
    if (!deliveryLine) {
      throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
    }

    const timeInSecondsBN = createBigNumber(timeElapsedHours).multipliedBy(3600);

    // 使用基于周期的正确计算
    const timeInSeconds = formatToThreeDecimalsNumber(timeInSecondsBN);
    const deliveryProcessing = this.calculateDeliveryProcessingByCycles(deliveryLine, timeInSeconds);
    const theoreticalGemIncrease = deliveryProcessing.gemProduced;
    const maxAllowedGemIncrease = formatToThreeDecimalsNumber(createBigNumber(theoreticalGemIncrease).multipliedBy(1.5));

    // 智能验证逻辑：如果前端请求 > 实际计算的1.5倍，使用实际计算；否则使用前端请求
    if (tempGem > maxAllowedGemIncrease) {
      return {
        isValid: false,
        reason: language === 'en'
          ? `Gem request (${tempGem.toFixed(3)}) exceeds maximum allowed (${maxAllowedGemIncrease.toFixed(3)}), using calculated value`
          : `GEM请求量 (${tempGem.toFixed(3)}) 超出最大允许值 (${maxAllowedGemIncrease.toFixed(3)})，使用计算值`,
        adjustedTempGem: theoreticalGemIncrease
      };
    }

    // 前端请求在1.5倍误差范围内，使用前端请求的数量
    return { isValid: true, adjustedTempGem: tempGem };
  }

  /**
   * 验证牛奶增量是否合理
   */
  private static async validateMilkIncrement(
    tempMilk: number,
    timeElapsedHours: number,
    walletId: number,
    transaction: Transaction,
    language: string = 'zh'
  ): Promise<{ isValid: boolean; reason?: string; adjustedTempMilk: number }> {

 
    // 计算出货线处理能力
    const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

    // 每个用户都有出货线，如果查询失败则抛出错误
    if (!deliveryLine) {
      throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
    }

    const timeInSecondsBN = createBigNumber(timeElapsedHours).multipliedBy(3600);

    // 使用基于周期的正确计算
    const timeInSeconds = formatToThreeDecimalsNumber(timeInSecondsBN);
    const actualFarmProduction = await this.calculateFarmProductionByCycles(walletId, timeInSeconds, transaction);
    const deliveryProcessing = this.calculateDeliveryProcessingByCycles(deliveryLine, timeInSeconds);

    const theoreticalNetMilkChange = formatToThreeDecimalsNumber(
      createBigNumber(actualFarmProduction).minus(deliveryProcessing.milkConsumed)
    );

    // 智能验证逻辑：允许1.5倍误差范围
    // 对于正增量：如果前端请求 > 理论计算的1.5倍，使用理论计算；否则使用前端请求
    // 对于负增量：如果前端请求的绝对值 > 理论计算绝对值的1.5倍，使用理论计算；否则使用前端请求
    const theoreticalNetMilkChangeBN = createBigNumber(theoreticalNetMilkChange);

    if (theoreticalNetMilkChangeBN.isGreaterThanOrEqualTo(0)) {
      // 理论上应该增加牛奶
      const maxAllowedIncreaseBN = theoreticalNetMilkChangeBN.multipliedBy(1.5);
      const maxAllowedIncrease = formatToThreeDecimalsNumber(maxAllowedIncreaseBN);

      if (tempMilk > maxAllowedIncrease) {
        return {
          isValid: false,
          reason: language === 'en'
            ? `Milk increase request (${tempMilk.toFixed(3)}) exceeds maximum allowed (${maxAllowedIncrease}), using calculated value`
            : `牛奶增加请求 (${tempMilk.toFixed(3)}) 超出最大允许值 (${maxAllowedIncrease})，使用计算值`,
          adjustedTempMilk: theoreticalNetMilkChange
        };
      }
    } else {
      // 理论上应该减少牛奶
      const maxAllowedDecreaseBN = theoreticalNetMilkChangeBN.multipliedBy(1.5); // 注意：这里是负数 * 1.5，结果更负
      const maxAllowedDecrease = formatToThreeDecimalsNumber(maxAllowedDecreaseBN);

      if (tempMilk < maxAllowedDecrease) {
        return {
          isValid: false,
          reason: language === 'en'
            ? `Milk decrease request (${tempMilk.toFixed(3)}) exceeds maximum allowed (${maxAllowedDecrease}), using calculated value`
            : `牛奶减少请求 (${tempMilk.toFixed(3)}) 超出最大允许值 (${maxAllowedDecrease})，使用计算值`,
          adjustedTempMilk: theoreticalNetMilkChange
        };
      }
    }

    // 前端请求在合理范围内，使用前端请求的数量
    return { isValid: true, adjustedTempMilk: tempMilk };
  }


  /**
   * 基于用户lastActiveTime计算应该获得的资源上限
   */
  private static async calculateTimeBasedResourceLimits(
    walletId: number,
    lastActiveTime: Date | null,
    deliveryLine: DeliveryLine | null,
    transaction: Transaction,
    language: string = 'zh'
  ): Promise<{
    gemLimit: number;
    pendingMilkLimit: number;
    timeElapsedHours: number;
    reason: string;
  }> {
    const now = new Date();

    // 如果没有lastActiveTime，设置为当前时间（新用户）
    if (!lastActiveTime) {
      return {
        gemLimit: 0,
        pendingMilkLimit: 0,
        timeElapsedHours: 0,
        reason: language === 'en' ? 'New user, no accumulated resources' : '新用户，无累积资源'
      };
    }

    // 计算时间差（小时）
    const lastActiveDate = new Date(lastActiveTime);
    const timeElapsedMs = now.getTime() - lastActiveDate.getTime();
    const timeElapsedHours = timeElapsedMs / (1000 * 60 * 60);

    // 如果时间差小于5秒，不给予资源
    if (timeElapsedHours < (5/3600)) {
      return {
        gemLimit: 0,
        pendingMilkLimit: 0,
        timeElapsedHours,
        reason: language === 'en' ? 'Too soon since last update (minimum 5 seconds)' : '距离上次更新时间太短（最少5秒）'
      };
    }

    // 如果时间差超过2分钟，判定为离线，不给予资源
    if (timeElapsedHours > (2/60)) {
      return {
        gemLimit: 0,
        pendingMilkLimit: 0,
        timeElapsedHours,
        reason: language === 'en' ? 'User offline (more than 2 minutes since last update)' : '用户离线（距离上次更新超过2分钟）'
      };
    }

    // 计算资源上限（基于周期的正确计算）
    const timeElapsedSeconds = timeElapsedHours * 3600;
    const actualFarmProduction = await this.calculateFarmProductionByCycles(walletId, timeElapsedSeconds, transaction);
    const pendingMilkLimit = actualFarmProduction;

    // 计算GEM上限（基于出货线处理能力）
    // 每个用户都有出货线，如果查询失败则抛出错误
    if (!deliveryLine) {
      throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
    }

    const deliveryProcessing = this.calculateDeliveryProcessingByCycles(deliveryLine, timeElapsedSeconds);
    const gemLimit = deliveryProcessing.gemProduced;

    const reason = language === 'en'
      ? `Based on ${timeElapsedHours.toFixed(2)} hours since last active`
      : `基于距离上次活跃${timeElapsedHours.toFixed(2)}小时`;

    return {
      gemLimit: formatToThreeDecimalsNumber(gemLimit),
      pendingMilkLimit: formatToThreeDecimalsNumber(pendingMilkLimit),
      timeElapsedHours,
      reason
    };
  }

  /**
   * 执行批量资源更新
   */
  public static async updateResources(
    userId: number,
    walletId: number,
    request: BatchResourceUpdateRequest,
    language: string = 'zh'
  ): Promise<BatchResourceUpdateResponse> {
    // 验证请求参数
    const validation = this.validateRequest(request);
    if (!validation.isValid) {
      throw new Error(`参数验证失败: ${validation.errors.join(', ')}`);
    }

    const transaction: Transaction = await sequelize.transaction();

    try {
      // 获取用户钱包
      const wallet = await UserWallet.findOne({
        where: { id: walletId, userId },
        transaction
      });

      if (!wallet) {
        await transaction.rollback();
        throw new Error('用户钱包不存在');
      }

      // 记录更新前状态
      const beforeUpdate = {
        gem: formatToThreeDecimalsNumber(wallet.gem || 0),
        pendingMilk: 0,
        lastActiveTime: wallet.lastActiveTime ? dayjs(wallet.lastActiveTime).format('YYYY-MM-DD HH:mm:ss') : null
      };

      // 获取出货线信息
      const deliveryLine = await DeliveryLine.findOne({
        where: { walletId },
        transaction
      });

      if (deliveryLine) {
        beforeUpdate.pendingMilk = formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0);
      }

      // 计算基于时间的资源上限
      const resourceLimits = await this.calculateTimeBasedResourceLimits(
        walletId,
        wallet.lastActiveTime as Date | null,
        deliveryLine,
        transaction,
        language
      );

      // 验证前端提交的资源数据
      let usedSystemCalculation = false;
      let systemCalculationReason = '';

      // 详细增减信息
      let gemIncreased = 0;
      let milkIncreased = 0;
      let milkDecreased = 0;

      // 计算生产速率信息（复用已有的变量）
      const farmProductionPerSecond = await this.calculateFarmProductionCapacity(walletId, transaction);

      // 计算GEM生产速率（复用已有的deliveryLine）
      let deliveryProcessingPerSecond = 0;
      let gemPerSecond = 0;

      if (deliveryLine) {
        deliveryProcessingPerSecond = deliveryLine.blockUnit / deliveryLine.deliverySpeed;
        gemPerSecond = deliveryLine.blockPrice / deliveryLine.deliverySpeed;
      }

      // 处理前端提交的增量数据
      let finalTempGem = 0;
      let finalTempMilk = 0;

      // 验证结果变量（在更大作用域中声明）
      let gemValidation = { isValid: true, adjustedTempGem: 0 };
      let milkValidation = { isValid: true, adjustedTempMilk: 0 };

      // 检查是否在有效时间窗口内（5秒-2分钟）
      const timeElapsedSecondsBN = createBigNumber(resourceLimits.timeElapsedHours).multipliedBy(3600);
      const timeElapsedSeconds = formatToThreeDecimalsNumber(timeElapsedSecondsBN);
      const isInValidTimeWindow = timeElapsedSeconds >= 5 && timeElapsedSeconds <= 120; // 5秒到2分钟

      if (!isInValidTimeWindow) {
        // 超出有效时间窗口，不计算任何资源
        finalTempGem = 0;
        finalTempMilk = 0;
        usedSystemCalculation = true;
        systemCalculationReason = resourceLimits.reason;
      } else if (request.gemRequest !== undefined || request.milkOperations !== undefined) {
        // 在有效时间窗口内，且前端提供了增量数据，进行验证
        const requestedGems = request.gemRequest || 0;
        const requestedMilkNet = this.parseMilkOperations(request.milkOperations);

        // 这里先记录前端请求值，稍后会用实际计算值覆盖
        if (request.milkOperations) {
          milkIncreased = request.milkOperations.produce || 0;
          milkDecreased = request.milkOperations.consume || 0;
        }
        gemIncreased = requestedGems; // GEM只能增加

        // 更新验证结果的初始值
        gemValidation = { isValid: true, adjustedTempGem: requestedGems };
        milkValidation = { isValid: true, adjustedTempMilk: requestedMilkNet };

        // 验证GEM增量（如果提供了）
        if (requestedGems > 0) {
          gemValidation = await this.validateGemIncrement(
            requestedGems,
            resourceLimits.timeElapsedHours,
            walletId,
            transaction,
            language
          );
        }

        // 验证牛奶增量（如果提供了）
        if (requestedMilkNet !== 0) {
          milkValidation = await this.validateMilkIncrement(
            requestedMilkNet,
            resourceLimits.timeElapsedHours,
            walletId,
            transaction,
            language
          );
        }

        // 应用验证结果
        if (!gemValidation.isValid || !milkValidation.isValid) {
          usedSystemCalculation = true;
          const reasons = [];
          if (!gemValidation.isValid && 'reason' in gemValidation) reasons.push(`GEM: ${gemValidation.reason}`);
          if (!milkValidation.isValid && 'reason' in milkValidation) reasons.push(`牛奶: ${milkValidation.reason}`);
          systemCalculationReason = reasons.join('; ');
        }

        finalTempGem = gemValidation.adjustedTempGem || 0;
        finalTempMilk = milkValidation.adjustedTempMilk || 0;
      } else {
        // 在有效时间窗口内，无参数调用，使用系统计算的资源增量
        finalTempGem = resourceLimits.gemLimit;
        finalTempMilk = resourceLimits.pendingMilkLimit;
      }

      // 处理GEM更新（GEM只能增加，不能减少）
      if (finalTempGem > 0) {
        const currentGemBN = createBigNumber(wallet.gem || 0);
        const gemIncrementBN = createBigNumber(finalTempGem);
        const newGemBN = currentGemBN.plus(gemIncrementBN);

        let updateType = usedSystemCalculation ? 'SYSTEM_CALCULATION' : 'BATCH_UPDATE';
        let reference = usedSystemCalculation
          ? (language === 'en' ? 'System Calculated Update' : '系统计算更新')
          : (language === 'en' ? 'Batch Resource Update' : '批量资源更新');

        // 更新GEM（只增加）
        wallet.gem = formatToThreeDecimalsNumber(newGemBN);

        // GEM的details已经在前面通过协调计算设置，这里不需要重复设置

        // 创建GEM历史记录（使用实际给用户的数量）
        const actualGemAdded = formatToThreeDecimalsNumber(gemIncrementBN);
        await WalletHistory.create({
          userId,
          walletId,
          currency: 'GEM',
          amount: actualGemAdded,
          reference,
          action: 'IN',
          category: updateType,
          credit_type: updateType,
          fe_display_remark: language === 'en'
            ? `Added ${actualGemAdded} GEM${usedSystemCalculation ? ' - System calculated due to unreasonable request' : ''}`
            : `增加了 ${actualGemAdded} GEM${usedSystemCalculation ? ' - 因请求不合理使用系统计算值' : ''}`,
          developer_remark: `批量资源更新: +${actualGemAdded} GEM${usedSystemCalculation ? ' [系统计算]' : ''}`
        }, { transaction });
      }

      // 先设置默认的details值，稍后会根据实际执行结果更新
      if (!isInValidTimeWindow) {
        // 用户离线，不显示任何增减量
        milkIncreased = 0;
        milkDecreased = 0;
        gemIncreased = 0;
      }

      // 处理PendingMilk更新
      if (finalTempMilk !== 0) {
        // 每个用户都有出货线，如果查询失败则抛出错误
        if (!deliveryLine) {
          throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
        }
        const currentPendingMilkBN = createBigNumber(deliveryLine.pendingMilk || 0);
        const milkIncrementBN = createBigNumber(finalTempMilk);
        const newPendingMilkBN = currentPendingMilkBN.plus(milkIncrementBN);

        let updateType = usedSystemCalculation ? 'SYSTEM_CALCULATION' : 'BATCH_UPDATE';
        let reference = usedSystemCalculation
          ? (language === 'en' ? 'System Calculated Update' : '系统计算更新')
          : (language === 'en' ? 'Batch Resource Update' : '批量资源更新');

        // 计算实际的牛奶变化量
        let actualMilkChange: number;

        // 确保PendingMilk不会变成负数
        if (newPendingMilkBN.isNegative()) {
          deliveryLine.pendingMilk = 0;
          actualMilkChange = formatToThreeDecimalsNumber(currentPendingMilkBN.negated());
        } else {
          deliveryLine.pendingMilk = formatToThreeDecimalsNumber(newPendingMilkBN);
          actualMilkChange = formatToThreeDecimalsNumber(milkIncrementBN);
        }

        // 牛奶的details已经在前面通过协调计算设置，这里不需要重复设置

        await deliveryLine.save({ transaction });

        // 创建PendingMilk历史记录
        await WalletHistory.create({
          userId,
          walletId,
          currency: 'MILK',
          amount: Math.abs(actualMilkChange),
          reference,
          action: actualMilkChange >= 0 ? 'IN' : 'OUT',
          category: updateType,
          credit_type: updateType,
          fe_display_remark: language === 'en'
            ? `${actualMilkChange >= 0 ? 'Added' : 'Reduced'} ${Math.abs(actualMilkChange)} pending milk${usedSystemCalculation ? ' - System calculated due to unreasonable request' : ''}`
            : `${actualMilkChange >= 0 ? '增加了' : '减少了'} ${Math.abs(actualMilkChange)} 待处理牛奶${usedSystemCalculation ? ' - 因请求不合理使用系统计算值' : ''}`,
          developer_remark: `批量资源更新: ${actualMilkChange >= 0 ? '+' : ''}${actualMilkChange} 待处理牛奶${usedSystemCalculation ? ' [系统计算]' : ''}`
        }, { transaction });
      }

      // 统一更新lastActiveTime
      const now = new Date();
      wallet.lastActiveTime = now;
      await wallet.save({ transaction });

      await transaction.commit();

      // 根据实际执行结果设置details（显示实际可执行的变化量）
      if (isInValidTimeWindow) {
        // 计算实际的资源变化
        const actualGemChange = formatToThreeDecimalsNumber(wallet.gem || 0) - beforeUpdate.gem;
        const actualMilkChange = (deliveryLine ? formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0) : 0) - beforeUpdate.pendingMilk;

        // 设置GEM的实际增量
        gemIncreased = Math.max(0, actualGemChange);

        // 设置牛奶的实际增减量
        if (actualMilkChange >= 0) {
          milkIncreased = actualMilkChange;
          milkDecreased = 0;
        } else {
          milkIncreased = 0;
          milkDecreased = Math.abs(actualMilkChange);
        }

        // 如果没有任何实际更新，尝试使用协调计算来显示理论值
        if (finalTempGem === 0 && finalTempMilk === 0) {
          const currentPendingMilk = beforeUpdate.pendingMilk;
          const coordinatedResult = await this.calculateCoordinatedProduction(
            walletId,
            timeElapsedSeconds,
            currentPendingMilk,
            transaction
          );

          // 只有在没有实际变化时才显示理论值
          if (actualGemChange === 0 && actualMilkChange === 0) {
            milkIncreased = coordinatedResult.farmProduced;
            milkDecreased = coordinatedResult.deliveryConsumed;
            gemIncreased = coordinatedResult.gemProduced;
          }
        }
      }

      // 构建响应数据
      // 每个用户都有出货线，如果查询失败则抛出错误
      if (!deliveryLine) {
        throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
      }

      const afterUpdate = {
        gem: formatToThreeDecimalsNumber(wallet.gem || 0),
        pendingMilk: formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0),
        lastActiveTime: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
      };

      return {
        success: true,
        data: {
          beforeUpdate,
          afterUpdate,
          changes: {
            ...(usedSystemCalculation && {
              usedSystemCalculation: true,
              systemCalculationReason
            }),
            productionRates: {
              farmMilkPerSecond: formatToThreeDecimalsNumber(farmProductionPerSecond),
              deliveryMilkPerSecond: formatToThreeDecimalsNumber(deliveryProcessingPerSecond),
              gemPerSecond: formatToThreeDecimalsNumber(gemPerSecond),
              timeElapsedSeconds: timeElapsedSeconds
            },
            details: {
              gem: {
                increased: gemIncreased
              },
              milk: {
                increased: milkIncreased,
                decreased: milkDecreased
              }
            }
          },
          timestamp: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
        },
        message: language === 'en' ? 'Resources updated successfully' : '资源更新成功'
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

export default BatchResourceUpdateService;
